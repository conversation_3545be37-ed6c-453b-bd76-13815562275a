import { supabase, handleSupabaseError } from '../lib/supabase';
import { Database } from '../types/database';

type Tables = Database['public']['Tables'];
type BlogPost = Tables['blog_posts']['Row'];
type BookSummary = Tables['book_summaries']['Row'];
type BusinessIdea = Tables['business_ideas']['Row'];
type DigitalProduct = Tables['digital_products']['Row'];
type Category = Tables['categories']['Row'];
type Profile = Tables['profiles']['Row'];

// Blog Posts API
export const blogPostsApi = {
  async getAll(options?: {
    limit?: number;
    offset?: number;
    category?: string;
    search?: string;
    featured?: boolean;
  }) {
    try {
      let query = supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (full_name, avatar_url),
          categories:category_id (name, slug, color)
        `)
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (options?.category) {
        query = query.eq('categories.slug', options.category);
      }

      if (options?.search) {
        query = query.textSearch('title', options.search);
      }

      if (options?.featured) {
        query = query.eq('is_featured', true);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async getBySlug(slug: string) {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (full_name, avatar_url, bio),
          categories:category_id (name, slug, color)
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;

      // Increment view count
      await supabase
        .from('blog_posts')
        .update({ view_count: data.view_count + 1 })
        .eq('id', data.id);

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async incrementLikes(id: string) {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('like_count')
        .eq('id', id)
        .single();

      if (error) throw error;

      const { error: updateError } = await supabase
        .from('blog_posts')
        .update({ like_count: data.like_count + 1 })
        .eq('id', id);

      if (updateError) throw updateError;

      return data.like_count + 1;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Book Summaries API
export const bookSummariesApi = {
  async getAll(options?: {
    limit?: number;
    offset?: number;
    category?: string;
    search?: string;
  }) {
    try {
      let query = supabase
        .from('book_summaries')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (options?.category) {
        query = query.eq('categories.slug', options.category);
      }

      if (options?.search) {
        query = query.or(`title.ilike.%${options.search}%,author.ilike.%${options.search}%`);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async getBySlug(slug: string) {
    try {
      const { data, error } = await supabase
        .from('book_summaries')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;

      // Increment view count
      await supabase
        .from('book_summaries')
        .update({ view_count: data.view_count + 1 })
        .eq('id', data.id);

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Business Ideas API
export const businessIdeasApi = {
  async getAll(options?: {
    limit?: number;
    offset?: number;
    category?: string;
    investment?: string;
    difficulty?: string;
    search?: string;
  }) {
    try {
      let query = supabase
        .from('business_ideas')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (options?.category) {
        query = query.eq('categories.slug', options.category);
      }

      if (options?.investment) {
        query = query.eq('investment_level', options.investment);
      }

      if (options?.difficulty) {
        query = query.eq('difficulty', options.difficulty);
      }

      if (options?.search) {
        query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async getBySlug(slug: string) {
    try {
      const { data, error } = await supabase
        .from('business_ideas')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;

      // Increment view count
      await supabase
        .from('business_ideas')
        .update({ view_count: data.view_count + 1 })
        .eq('id', data.id);

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Digital Products API
export const digitalProductsApi = {
  async getAll(options?: {
    limit?: number;
    offset?: number;
    category?: string;
    type?: string;
    search?: string;
  }) {
    try {
      let query = supabase
        .from('digital_products')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (options?.category) {
        query = query.eq('categories.slug', options.category);
      }

      if (options?.type) {
        query = query.eq('product_type', options.type);
      }

      if (options?.search) {
        query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async getBySlug(slug: string) {
    try {
      const { data, error } = await supabase
        .from('digital_products')
        .select(`
          *,
          categories:category_id (name, slug, color)
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Categories API
export const categoriesApi = {
  async getAll() {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// User Favorites API
export const favoritesApi = {
  async getUserFavorites(userId: string, contentType?: string) {
    try {
      let query = supabase
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId);

      if (contentType) {
        query = query.eq('content_type', contentType);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async addFavorite(userId: string, contentType: string, contentId: string) {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          content_type: contentType as any,
          content_id: contentId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async removeFavorite(userId: string, contentType: string, contentId: string) {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('content_type', contentType)
        .eq('content_id', contentId);

      if (error) throw error;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Newsletter API
export const newsletterApi = {
  async subscribe(email: string, name?: string) {
    try {
      const { data, error } = await supabase
        .from('newsletter_subscribers')
        .upsert({
          email,
          name,
          status: 'active',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  async unsubscribe(email: string) {
    try {
      const { error } = await supabase
        .from('newsletter_subscribers')
        .update({
          status: 'unsubscribed',
          unsubscribed_at: new Date().toISOString(),
        })
        .eq('email', email);

      if (error) throw error;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },
};

// Analytics API
export const analyticsApi = {
  async trackEvent(eventType: string, eventData: Record<string, any>, userId?: string) {
    try {
      const { error } = await supabase
        .from('analytics_events')
        .insert({
          user_id: userId || null,
          event_type: eventType,
          event_data: eventData,
          session_id: sessionStorage.getItem('session_id') || undefined,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Analytics tracking error:', error);
      // Don't throw error for analytics to avoid disrupting user experience
    }
  },
};