import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X, BookOpen } from 'lucide-react';
import { Button } from '../ui/Button';

const navigation = [
  { name: 'Home', href: '/' },
  { name: 'Blog', href: '/blog' },
  { name: 'Book Summaries', href: '/book-summaries' },
  { name: 'Business Ideas', href: '/business-ideas' },
  { name: 'Digital Products', href: '/digital-products' },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
];

export const MobileNav: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="lg:hidden">
      {/* Mobile menu button */}
      <div className="flex items-center justify-between h-16 px-4 bg-white border-b border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-br from-african-500 to-primary-600 rounded-lg flex items-center justify-center">
            <BookOpen className="w-5 h-5 text-white" />
          </div>
          <span className="ml-3 text-xl font-bold text-gray-900">AfricaLearn</span>
        </div>
        <Button
          variant="ghost"
          onClick={() => setIsOpen(!isOpen)}
          icon={isOpen ? X : Menu}
          className="p-2"
        >
          <span className="sr-only">Open menu</span>
        </Button>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="absolute top-16 inset-x-0 bg-white border-b border-gray-200 shadow-lg z-50">
          <nav className="px-4 py-4 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={() => setIsOpen(false)}
                className={({ isActive }) =>
                  `block px-3 py-2 text-base font-medium rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`
                }
              >
                {item.name}
              </NavLink>
            ))}
          </nav>
        </div>
      )}
    </div>
  );
};