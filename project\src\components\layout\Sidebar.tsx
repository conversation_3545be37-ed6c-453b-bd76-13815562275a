import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, 
  BookOpen, 
  FileText, 
  Lightbulb, 
  ShoppingBag, 
  User, 
  Mail,
  Award,
  LogIn,
  LogOut,
  Settings
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { AuthModal } from '../auth/AuthModal';
import { Button } from '../ui/Button';

const navigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Blog', href: '/blog', icon: FileText },
  { name: 'Book Summaries', href: '/book-summaries', icon: BookOpen },
  { name: 'Business Ideas', href: '/business-ideas', icon: Lightbulb },
  { name: 'Digital Products', href: '/digital-products', icon: ShoppingBag },
  { name: 'About', href: '/about', icon: Award },
  { name: 'Contact', href: '/contact', icon: Mail },
];

export const Sidebar: React.FC = () => {
  const { user, profile, signOut } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthMode(mode);
    setShowAuthModal(true);
  };

  return (
    <>
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 lg:block hidden">
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center h-16 px-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-african-500 to-primary-600 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="ml-3 text-xl font-bold text-gray-900">AfricaLearn</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`
                }
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </NavLink>
            ))}
          </nav>

          {/* User Profile / Auth */}
          <div className="p-4 border-t border-gray-200">
            {user && profile ? (
              <div className="space-y-3">
                <div className="flex items-center">
                  <img
                    className="w-10 h-10 rounded-full object-cover"
                    src={profile.avatar_url || "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=150"}
                    alt={profile.full_name || 'User'}
                  />
                  <div className="ml-3 flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {profile.full_name || 'User'}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {profile.role === 'admin' ? 'Administrator' : 
                       profile.role === 'author' ? 'Author' : 'Explorer'}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={Settings}
                    className="flex-1"
                  >
                    Settings
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={LogOut}
                    onClick={signOut}
                    className="flex-1"
                  >
                    Sign Out
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Button
                  onClick={() => handleAuthClick('signin')}
                  icon={LogIn}
                  className="w-full"
                  size="sm"
                >
                  Sign In
                </Button>
                <Button
                  onClick={() => handleAuthClick('signup')}
                  variant="outline"
                  icon={User}
                  className="w-full"
                  size="sm"
                >
                  Sign Up
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode={authMode}
      />
    </>
  );
};