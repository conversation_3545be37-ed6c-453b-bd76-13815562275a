import React from 'react';
import { useForm } from 'react-hook-form';
import { Mail, CheckCircle } from 'lucide-react';
import { Button } from '../ui/Button';

interface NewsletterForm {
  name: string;
  email: string;
}

export const Newsletter: React.FC = () => {
  const { register, handleSubmit, formState: { errors }, reset } = useForm<NewsletterForm>();
  const [isSubmitted, setIsSubmitted] = React.useState(false);

  const onSubmit = (data: NewsletterForm) => {
    console.log('Newsletter signup:', data);
    setIsSubmitted(true);
    reset();
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  return (
    <div className="bg-gradient-to-r from-primary-600 to-african-600 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <Mail className="w-8 h-8 text-white" />
          </div>
        </div>
        
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
          Stay Updated with African Insights
        </h2>
        <p className="text-xl text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
          Get weekly summaries, business insights, and exclusive content delivered to your inbox.
        </p>

        {isSubmitted ? (
          <div className="flex items-center justify-center space-x-2 text-white">
            <CheckCircle className="w-6 h-6" />
            <span className="text-lg font-medium">Thank you for subscribing!</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="max-w-lg mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <input
                  {...register('name', { required: 'Name is required' })}
                  type="text"
                  placeholder="Your name"
                  className="w-full px-4 py-3 rounded-lg border border-white border-opacity-30 bg-white bg-opacity-10 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:bg-opacity-20"
                />
                {errors.name && (
                  <p className="text-red-200 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>
              <div>
                <input
                  {...register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  type="email"
                  placeholder="Your email"
                  className="w-full px-4 py-3 rounded-lg border border-white border-opacity-30 bg-white bg-opacity-10 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:bg-opacity-20"
                />
                {errors.email && (
                  <p className="text-red-200 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>
            </div>
            
            <Button 
              type="submit" 
              size="lg" 
              className="w-full bg-white text-primary-600 hover:bg-gray-100 focus:ring-white"
            >
              Subscribe Now
            </Button>
          </form>
        )}

        <p className="text-white text-opacity-70 text-sm mt-4">
          Join 10,000+ readers • Unsubscribe anytime • No spam, ever
        </p>
      </div>
    </div>
  );
};