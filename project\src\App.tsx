import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './hooks/useAuth';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { Analytics } from './components/common/Analytics';
import { Layout } from './components/layout/Layout';
import { Home } from './pages/Home';
import { Blog } from './pages/Blog';
import { BookSummaries } from './pages/BookSummaries';
import { BusinessIdeas } from './pages/BusinessIdeas';
import { DigitalProducts } from './pages/DigitalProducts';
import { About } from './pages/About';
import { Contact } from './pages/Contact';

function App() {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <AuthProvider>
          <Router>
            <Analytics />
            <Layout>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/blog" element={<Blog />} />
                <Route path="/book-summaries" element={<BookSummaries />} />
                <Route path="/business-ideas" element={<BusinessIdeas />} />
                <Route path="/digital-products" element={<DigitalProducts />} />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />
              </Routes>
            </Layout>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </Router>
        </AuthProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;