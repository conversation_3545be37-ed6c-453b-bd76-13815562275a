import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'africalearn-web@1.0.0',
    },
  },
});

// Enhanced error handling
export const handleSupabaseError = (error: any) => {
  console.error('Supabase Error:', error);
  
  if (error?.code === 'PGRST116') {
    return 'No data found';
  }
  
  if (error?.code === '23505') {
    return 'This item already exists';
  }
  
  if (error?.code === '42501') {
    return 'You do not have permission to perform this action';
  }
  
  return error?.message || 'An unexpected error occurred';
};

// Connection health check
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('count')
      .limit(1);
    
    if (error) throw error;
    return { connected: true, timestamp: new Date().toISOString() };
  } catch (error) {
    return { connected: false, error: handleSupabaseError(error) };
  }
};