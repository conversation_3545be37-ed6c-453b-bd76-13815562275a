/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#1e40af',
          700: '#1d4ed8',
          800: '#1e3a8a',
          900: '#1e40af',
        },
        african: {
          50: '#fef7ed',
          100: '#fde8d3',
          200: '#fbd3a7',
          300: '#f7b370',
          400: '#f18937',
          500: '#ed6611',
          600: '#de4c07',
          700: '#b73708',
          800: '#922c0e',
          900: '#78250f',
        },
        earth: {
          50: '#f6f3f0',
          100: '#e8e0d8',
          200: '#d5c4b3',
          300: '#bfa086',
          400: '#a8825f',
          500: '#8b6914',
          600: '#6d4c0c',
          700: '#583e0a',
          800: '#4a330c',
          900: '#3f2c0c',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};