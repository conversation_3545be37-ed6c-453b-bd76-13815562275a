import React from 'react';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { BookOpen, Users, Award, Target, ArrowRight } from 'lucide-react';

export const About: React.FC = () => {
  const stats = [
    { icon: BookOpen, label: 'Book Summaries', value: '500+' },
    { icon: Users, label: 'Active Readers', value: '50K+' },
    { icon: Award, label: 'Business Ideas', value: '1,200+' },
    { icon: Target, label: 'Success Stories', value: '2,500+' },
  ];

  const team = [
    {
      name: '<PERSON><PERSON>',
      role: 'Founder & CEO',
      image: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=300',
      bio: 'Passionate about African literature and entrepreneurship, <PERSON><PERSON> founded AfricaLearn to bridge the knowledge gap across the continent.'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Content Director',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?w=300',
      bio: 'Former journalist and author, <PERSON><PERSON><PERSON> leads our content strategy and ensures every piece delivers real value to our community.'
    },
    {
      name: '<PERSON><PERSON>-<PERSON>',
      role: 'Community Manager',
      image: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg?w=300',
      bio: 'With a background in digital marketing, Fatima builds and nurtures our growing community of African entrepreneurs and learners.'
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Empowering Africa Through
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-african-500 to-primary-600">
                  Knowledge & Innovation
                </span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                We believe that Africa's future lies in the hands of informed, empowered individuals who can turn knowledge into action. Our platform bridges the gap between wisdom and implementation.
              </p>
              <Button size="lg" icon={ArrowRight} iconPosition="right">
                Join Our Mission
              </Button>
            </div>
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/5380664/pexels-photo-5380664.jpeg?w=600"
                alt="African innovation"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-african-500 to-primary-600 rounded-full flex items-center justify-center">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">50K+</p>
                    <p className="text-sm text-gray-600">Lives Impacted</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-gradient-to-r from-primary-600 to-african-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-white text-opacity-90 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Mission
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              To democratize access to knowledge and create a thriving ecosystem where African minds can learn, grow, and innovate together.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-african-100 to-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <BookOpen className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Knowledge Access</h3>
                <p className="text-gray-600">
                  Making world-class knowledge accessible to every African, regardless of location or economic status.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-african-100 to-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Users className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Community Building</h3>
                <p className="text-gray-600">
                  Fostering connections between entrepreneurs, innovators, and thought leaders across Africa.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-african-100 to-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Target className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Practical Impact</h3>
                <p className="text-gray-600">
                  Transforming knowledge into actionable insights that drive real-world change and progress.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Team Section */}
      <div className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Passionate individuals dedicated to advancing African knowledge and innovation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <Card key={index}>
                <CardContent className="p-8 text-center">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-24 h-24 rounded-full mx-auto mb-6 object-cover"
                  />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {member.name}
                  </h3>
                  <p className="text-primary-600 font-medium mb-4">
                    {member.role}
                  </p>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {member.bio}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-african-600 to-primary-600 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Join Our Community?
          </h2>
          <p className="text-xl text-white text-opacity-90 mb-8">
            Connect with thousands of African entrepreneurs, innovators, and knowledge seekers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-primary-600 hover:bg-gray-100"
            >
              Get Started Today
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-white text-white hover:bg-white hover:text-primary-600"
            >
              Learn More
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};