import React from 'react';
import { ArrowR<PERSON>, Play } from 'lucide-react';
import { But<PERSON> } from '../ui/Button';

export const Hero: React.FC = () => {
  return (
    <div className="relative bg-gradient-to-br from-earth-50 via-white to-african-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-fade-in">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
              Unlock African
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-african-500 to-primary-600">
                Knowledge & Wisdom
              </span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 leading-relaxed">
              Discover the rich tapestry of African literature, innovative business ideas, 
              and transformative insights that are shaping the continent's future.
            </p>
            <div className="mt-8 flex flex-wrap gap-4">
              <Button size="lg" icon={ArrowRight} iconPosition="right">
                Start Exploring
              </Button>
              <Button variant="outline" size="lg" icon={Play}>
                Watch Intro
              </Button>
            </div>
            
            {/* Stats */}
            <div className="mt-12 grid grid-cols-3 gap-8">
              <div>
                <div className="text-2xl font-bold text-gray-900">500+</div>
                <div className="text-sm text-gray-500">Book Summaries</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">1,200+</div>
                <div className="text-sm text-gray-500">Business Ideas</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">50K+</div>
                <div className="text-sm text-gray-500">Active Readers</div>
              </div>
            </div>
          </div>

          {/* Featured Book Display */}
          <div className="relative animate-slide-up">
            <div className="relative bg-white rounded-2xl shadow-2xl p-8 transform rotate-2 hover:rotate-0 transition-transform duration-300">
              <div className="flex items-start space-x-6">
                <img
                  src="https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?w=200"
                  alt="Featured Book"
                  className="w-32 h-40 object-cover rounded-lg shadow-md"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">Things Fall Apart</h3>
                  <p className="text-sm text-gray-600 mt-1">by Chinua Achebe</p>
                  <div className="mt-3">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="w-4 h-4 bg-yellow-400 rounded-sm"></div>
                      ))}
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      A powerful exploration of colonialism's impact on traditional African society.
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-xs text-gray-400">155 / 300 pages</span>
                <Button size="sm">Continue Reading</Button>
              </div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-african-100 rounded-full flex items-center justify-center">
              <span className="text-african-600 font-bold">+50</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};