import React, { useState } from 'react';
import { SearchBar } from '../components/ui/SearchBar';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Lightbulb, DollarSign, Clock, TrendingUp, Users, ArrowRight } from 'lucide-react';
import { mockBusinessIdeas } from '../data/mockData';

export const BusinessIdeas: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedInvestment, setSelectedInvestment] = useState('all');

  const categories = ['all', 'Energy', 'Food & Beverage', 'Digital Services', 'Agriculture', 'Healthcare', 'Education'];
  const investmentLevels = ['all', 'Low', 'Medium', 'High'];

  const filteredIdeas = mockBusinessIdeas.filter(idea => {
    const matchesSearch = idea.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          idea.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || idea.category === selectedCategory;
    const matchesInvestment = selectedInvestment === 'all' || idea.investmentLevel === selectedInvestment;
    return matchesSearch && matchesCategory && matchesInvestment;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-700';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-700';
      case 'Advanced': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getInvestmentColor = (level: string) => {
    switch (level) {
      case 'Low': return 'bg-green-100 text-green-700';
      case 'Medium': return 'bg-yellow-100 text-yellow-700';
      case 'High': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-african-500 to-primary-600 rounded-2xl flex items-center justify-center">
                <Lightbulb className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              African Business Opportunities
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover innovative business ideas tailored for the African market with detailed implementation guides
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8 space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <SearchBar
              placeholder="Search business ideas..."
              value={searchQuery}
              onChange={setSearchQuery}
              className="max-w-md"
            />
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <TrendingUp className="w-4 h-4" />
              <span>{filteredIdeas.length} opportunities available</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700">Category:</span>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="capitalize"
                >
                  {category}
                </Button>
              ))}
            </div>
            
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700">Investment:</span>
              {investmentLevels.map((level) => (
                <Button
                  key={level}
                  variant={selectedInvestment === level ? 'secondary' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedInvestment(level)}
                  className="capitalize"
                >
                  {level}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Business Ideas Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredIdeas.map((idea) => (
            <Card key={idea.id} className="group cursor-pointer h-full">
              <CardContent className="p-6 h-full flex flex-col">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                      {idea.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {idea.description}
                    </p>
                  </div>
                  <div className="ml-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-african-100 to-primary-100 rounded-xl flex items-center justify-center">
                      <Lightbulb className="w-6 h-6 text-primary-600" />
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">
                    {idea.category}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(idea.difficulty)}`}>
                    {idea.difficulty}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getInvestmentColor(idea.investmentLevel)}`}>
                    {idea.investmentLevel} Investment
                  </span>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center">
                    <Users className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">Market: {idea.potentialMarket}</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">Revenue: {idea.estimatedRevenue}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">Time to start: {idea.timeToStart}</span>
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Required Skills:</h4>
                  <div className="flex flex-wrap gap-1">
                    {idea.requiredSkills.map((skill, index) => (
                      <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-auto pt-4 border-t border-gray-100">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    icon={ArrowRight}
                    iconPosition="right"
                  >
                    View Complete Guide
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredIdeas.length === 0 && (
          <div className="text-center py-12">
            <Lightbulb className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No business ideas found</h3>
            <p className="text-gray-500 mb-4">Try adjusting your search or filters</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedInvestment('all');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};