import { useEffect, useState, useCallback } from 'react';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { Database } from '../types/database';

type Tables = keyof Database['public']['Tables'];
type TableRow<T extends Tables> = Database['public']['Tables'][T]['Row'];

interface UseRealtimeOptions<T extends Tables> {
  table: T;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  onInsert?: (payload: RealtimePostgresChangesPayload<TableRow<T>>) => void;
  onUpdate?: (payload: RealtimePostgresChangesPayload<TableRow<T>>) => void;
  onDelete?: (payload: RealtimePostgresChangesPayload<TableRow<T>>) => void;
}

export const useRealtime = <T extends Tables>({
  table,
  filter,
  event = '*',
  onInsert,
  onUpdate,
  onDelete,
}: UseRealtimeOptions<T>) => {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = useCallback((payload: RealtimePostgresChangesPayload<TableRow<T>>) => {
    try {
      switch (payload.eventType) {
        case 'INSERT':
          onInsert?.(payload);
          break;
        case 'UPDATE':
          onUpdate?.(payload);
          break;
        case 'DELETE':
          onDelete?.(payload);
          break;
      }
    } catch (err) {
      console.error('Error handling realtime change:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, [onInsert, onUpdate, onDelete]);

  useEffect(() => {
    const channelName = `${table}_changes_${Date.now()}`;
    const newChannel = supabase.channel(channelName);

    // Configure the channel based on filter
    let channelConfig = newChannel.on(
      'postgres_changes',
      {
        event,
        schema: 'public',
        table,
        ...(filter && { filter }),
      },
      handleChange
    );

    // Subscribe to the channel
    channelConfig.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        setConnected(true);
        setError(null);
      } else if (status === 'CHANNEL_ERROR') {
        setConnected(false);
        setError('Failed to connect to realtime channel');
      } else if (status === 'TIMED_OUT') {
        setConnected(false);
        setError('Realtime connection timed out');
      } else if (status === 'CLOSED') {
        setConnected(false);
      }
    });

    setChannel(newChannel);

    // Cleanup function
    return () => {
      if (newChannel) {
        supabase.removeChannel(newChannel);
      }
    };
  }, [table, filter, event, handleChange]);

  const disconnect = useCallback(() => {
    if (channel) {
      supabase.removeChannel(channel);
      setChannel(null);
      setConnected(false);
    }
  }, [channel]);

  return {
    connected,
    error,
    disconnect,
  };
};

// Hook for real-time data fetching with automatic updates
export const useRealtimeData = <T extends Tables>(
  table: T,
  query?: (queryBuilder: any) => any,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<TableRow<T>[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initial data fetch
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let queryBuilder = supabase.from(table).select('*');
      
      if (query) {
        queryBuilder = query(queryBuilder);
      }

      const { data: fetchedData, error: fetchError } = await queryBuilder;

      if (fetchError) throw fetchError;
      setData(fetchedData);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [table, query, ...dependencies]);

  // Set up realtime subscription
  useRealtime({
    table,
    onInsert: (payload) => {
      setData(current => current ? [...current, payload.new as TableRow<T>] : [payload.new as TableRow<T>]);
    },
    onUpdate: (payload) => {
      setData(current => 
        current?.map(item => 
          (item as any).id === (payload.new as any).id ? payload.new as TableRow<T> : item
        ) || null
      );
    },
    onDelete: (payload) => {
      setData(current => 
        current?.filter(item => (item as any).id !== (payload.old as any).id) || null
      );
    },
  });

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
  };
};