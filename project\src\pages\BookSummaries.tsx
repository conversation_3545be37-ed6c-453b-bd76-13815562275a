import React, { useState } from 'react';
import { SearchBar } from '../components/ui/SearchBar';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Star, BookOpen, Calendar, ArrowRight } from 'lucide-react';
import { mockBookSummaries } from '../data/mockData';

export const BookSummaries: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', 'Literature', 'Philosophy', 'History', 'Business', 'Culture'];

  const filteredBooks = mockBookSummaries.filter(book => {
    const matchesSearch = book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          book.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          book.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              African Literature & Wisdom
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover profound insights from Africa's greatest minds through carefully curated book summaries
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8 space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <SearchBar
              placeholder="Search books, authors, or topics..."
              value={searchQuery}
              onChange={setSearchQuery}
              className="max-w-md"
            />
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <BookOpen className="w-4 h-4" />
              <span>{filteredBooks.length} books available</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="capitalize"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Featured Book - First book highlighted */}
        {filteredBooks.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured This Week</h2>
            <Card className="overflow-hidden">
              <div className="lg:flex">
                <div className="lg:w-1/3">
                  <img
                    src={filteredBooks[0].coverImage}
                    alt={filteredBooks[0].title}
                    className="w-full h-64 lg:h-full object-cover"
                  />
                </div>
                <CardContent className="lg:w-2/3 p-8">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="bg-african-100 text-african-700 px-3 py-1 rounded-full text-sm font-medium">
                      {filteredBooks[0].category}
                    </span>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(filteredBooks[0].rating) 
                              ? 'fill-yellow-400 text-yellow-400' 
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className="ml-2 text-sm text-gray-600">
                        {filteredBooks[0].rating}
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">
                    {filteredBooks[0].title}
                  </h3>
                  <p className="text-lg text-gray-600 mb-4">
                    by {filteredBooks[0].author}
                  </p>
                  
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    {filteredBooks[0].description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {filteredBooks[0].publishedYear}
                      </div>
                      <div className="flex items-center">
                        <BookOpen className="w-4 h-4 mr-1" />
                        {filteredBooks[0].pages} pages
                      </div>
                    </div>
                    
                    <Button icon={ArrowRight} iconPosition="right">
                      Read Summary
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        )}

        {/* Books Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {filteredBooks.slice(1).map((book) => (
            <Card key={book.id} className="group cursor-pointer">
              <CardContent className="p-4">
                <div className="aspect-[3/4] mb-4">
                  <img
                    src={book.coverImage}
                    alt={book.title}
                    className="w-full h-full object-cover rounded-lg group-hover:shadow-xl transition-shadow duration-300"
                  />
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-900 text-sm group-hover:text-primary-600 transition-colors line-clamp-2">
                    {book.title}
                  </h3>
                  <p className="text-xs text-gray-600">{book.author}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-3 h-3 ${
                            i < Math.floor(book.rating) 
                              ? 'fill-yellow-400 text-yellow-400' 
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">{book.pages}p</span>
                  </div>
                  
                  <div className="pt-2">
                    <span className="text-xs text-primary-600 font-medium">
                      {book.category}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredBooks.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No books found</h3>
            <p className="text-gray-500 mb-4">Try adjusting your search or filters</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};