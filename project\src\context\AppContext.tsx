import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { BlogPost, BookSummary, BusinessIdea, DigitalProduct, User } from '../types';

interface AppState {
  user: User | null;
  blogPosts: BlogPost[];
  bookSummaries: BookSummary[];
  businessIdeas: BusinessIdea[];
  digitalProducts: DigitalProduct[];
  searchQuery: string;
  selectedCategory: string;
  isLoading: boolean;
}

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_BLOG_POSTS'; payload: BlogPost[] }
  | { type: 'SET_BOOK_SUMMARIES'; payload: BookSummary[] }
  | { type: 'SET_BUSINESS_IDEAS'; payload: BusinessIdea[] }
  | { type: 'SET_DIGITAL_PRODUCTS'; payload: DigitalProduct[] }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SELECTED_CATEGORY'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean };

const initialState: AppState = {
  user: null,
  blogPosts: [],
  bookSummaries: [],
  businessIdeas: [],
  digitalProducts: [],
  searchQuery: '',
  selectedCategory: 'all',
  isLoading: false,
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_BLOG_POSTS':
      return { ...state, blogPosts: action.payload };
    case 'SET_BOOK_SUMMARIES':
      return { ...state, bookSummaries: action.payload };
    case 'SET_BUSINESS_IDEAS':
      return { ...state, businessIdeas: action.payload };
    case 'SET_DIGITAL_PRODUCTS':
      return { ...state, digitalProducts: action.payload };
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_SELECTED_CATEGORY':
      return { ...state, selectedCategory: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};