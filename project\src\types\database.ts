export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          bio: string | null;
          location: string | null;
          website: string | null;
          is_verified: boolean;
          role: 'user' | 'author' | 'admin';
          preferences: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          location?: string | null;
          website?: string | null;
          is_verified?: boolean;
          role?: 'user' | 'author' | 'admin';
          preferences?: Record<string, any>;
        };
        Update: {
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          location?: string | null;
          website?: string | null;
          preferences?: Record<string, any>;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          color: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          name: string;
          slug: string;
          description?: string | null;
          color?: string;
        };
        Update: {
          name?: string;
          slug?: string;
          description?: string | null;
          color?: string;
        };
      };
      blog_posts: {
        Row: {
          id: string;
          title: string;
          slug: string;
          excerpt: string | null;
          content: string;
          featured_image: string | null;
          author_id: string;
          category_id: string | null;
          status: 'draft' | 'published' | 'archived';
          is_featured: boolean;
          read_time: number;
          view_count: number;
          like_count: number;
          published_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          title: string;
          slug: string;
          excerpt?: string | null;
          content: string;
          featured_image?: string | null;
          author_id: string;
          category_id?: string | null;
          status?: 'draft' | 'published' | 'archived';
          is_featured?: boolean;
          read_time?: number;
          published_at?: string | null;
        };
        Update: {
          title?: string;
          slug?: string;
          excerpt?: string | null;
          content?: string;
          featured_image?: string | null;
          category_id?: string | null;
          status?: 'draft' | 'published' | 'archived';
          is_featured?: boolean;
          read_time?: number;
          published_at?: string | null;
        };
      };
      book_summaries: {
        Row: {
          id: string;
          title: string;
          slug: string;
          author: string;
          description: string | null;
          summary: string;
          cover_image: string | null;
          category_id: string | null;
          rating: number;
          pages: number | null;
          published_year: number | null;
          isbn: string | null;
          key_insights: string[];
          status: 'draft' | 'published' | 'archived';
          view_count: number;
          like_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          title: string;
          slug: string;
          author: string;
          description?: string | null;
          summary: string;
          cover_image?: string | null;
          category_id?: string | null;
          rating?: number;
          pages?: number | null;
          published_year?: number | null;
          isbn?: string | null;
          key_insights?: string[];
          status?: 'draft' | 'published' | 'archived';
        };
        Update: {
          title?: string;
          slug?: string;
          author?: string;
          description?: string | null;
          summary?: string;
          cover_image?: string | null;
          category_id?: string | null;
          rating?: number;
          pages?: number | null;
          published_year?: number | null;
          isbn?: string | null;
          key_insights?: string[];
          status?: 'draft' | 'published' | 'archived';
        };
      };
      business_ideas: {
        Row: {
          id: string;
          title: string;
          slug: string;
          description: string;
          category_id: string | null;
          investment_level: 'Low' | 'Medium' | 'High';
          difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
          potential_market: string | null;
          required_skills: string[];
          estimated_revenue: string | null;
          time_to_start: string | null;
          detailed_guide: string | null;
          resources: Record<string, any>;
          status: 'draft' | 'published' | 'archived';
          view_count: number;
          like_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          title: string;
          slug: string;
          description: string;
          category_id?: string | null;
          investment_level: 'Low' | 'Medium' | 'High';
          difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
          potential_market?: string | null;
          required_skills?: string[];
          estimated_revenue?: string | null;
          time_to_start?: string | null;
          detailed_guide?: string | null;
          resources?: Record<string, any>;
          status?: 'draft' | 'published' | 'archived';
        };
        Update: {
          title?: string;
          slug?: string;
          description?: string;
          category_id?: string | null;
          investment_level?: 'Low' | 'Medium' | 'High';
          difficulty?: 'Beginner' | 'Intermediate' | 'Advanced';
          potential_market?: string | null;
          required_skills?: string[];
          estimated_revenue?: string | null;
          time_to_start?: string | null;
          detailed_guide?: string | null;
          resources?: Record<string, any>;
          status?: 'draft' | 'published' | 'archived';
        };
      };
      digital_products: {
        Row: {
          id: string;
          title: string;
          slug: string;
          description: string;
          price: number;
          original_price: number | null;
          category_id: string | null;
          product_type: 'ebook' | 'course' | 'template' | 'toolkit';
          image: string | null;
          features: string[];
          file_url: string | null;
          file_size: string | null;
          download_count: number;
          rating: number;
          status: 'draft' | 'published' | 'archived';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          title: string;
          slug: string;
          description: string;
          price: number;
          original_price?: number | null;
          category_id?: string | null;
          product_type: 'ebook' | 'course' | 'template' | 'toolkit';
          image?: string | null;
          features?: string[];
          file_url?: string | null;
          file_size?: string | null;
          rating?: number;
          status?: 'draft' | 'published' | 'archived';
        };
        Update: {
          title?: string;
          slug?: string;
          description?: string;
          price?: number;
          original_price?: number | null;
          category_id?: string | null;
          product_type?: 'ebook' | 'course' | 'template' | 'toolkit';
          image?: string | null;
          features?: string[];
          file_url?: string | null;
          file_size?: string | null;
          rating?: number;
          status?: 'draft' | 'published' | 'archived';
        };
      };
      user_favorites: {
        Row: {
          id: string;
          user_id: string;
          content_type: 'blog_post' | 'book_summary' | 'business_idea' | 'digital_product';
          content_id: string;
          created_at: string;
        };
        Insert: {
          user_id: string;
          content_type: 'blog_post' | 'book_summary' | 'business_idea' | 'digital_product';
          content_id: string;
        };
        Update: never;
      };
      user_progress: {
        Row: {
          id: string;
          user_id: string;
          content_type: 'blog_post' | 'book_summary';
          content_id: string;
          progress_percentage: number;
          completed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          content_type: 'blog_post' | 'book_summary';
          content_id: string;
          progress_percentage?: number;
          completed_at?: string | null;
        };
        Update: {
          progress_percentage?: number;
          completed_at?: string | null;
        };
      };
      newsletter_subscribers: {
        Row: {
          id: string;
          email: string;
          name: string | null;
          status: 'active' | 'unsubscribed' | 'bounced';
          preferences: Record<string, any>;
          subscribed_at: string;
          unsubscribed_at: string | null;
        };
        Insert: {
          email: string;
          name?: string | null;
          status?: 'active' | 'unsubscribed' | 'bounced';
          preferences?: Record<string, any>;
        };
        Update: {
          name?: string | null;
          status?: 'active' | 'unsubscribed' | 'bounced';
          preferences?: Record<string, any>;
          unsubscribed_at?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}