export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  authorImage: string;
  publishedAt: string;
  category: string;
  tags: string[];
  featuredImage: string;
  readTime: number;
  slug: string;
}

export interface BookSummary {
  id: string;
  title: string;
  author: string;
  description: string;
  summary: string;
  coverImage: string;
  category: string;
  rating: number;
  pages: number;
  publishedYear: number;
  keyInsights: string[];
  slug: string;
}

export interface BusinessIdea {
  id: string;
  title: string;
  description: string;
  category: string;
  investmentLevel: 'Low' | 'Medium' | 'High';
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  potentialMarket: string;
  requiredSkills: string[];
  estimatedRevenue: string;
  timeToStart: string;
  slug: string;
}

export interface DigitalProduct {
  id: string;
  title: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  type: 'ebook' | 'course' | 'template' | 'toolkit';
  image: string;
  features: string[];
  downloadCount: number;
  rating: number;
  slug: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  joinedAt: string;
}