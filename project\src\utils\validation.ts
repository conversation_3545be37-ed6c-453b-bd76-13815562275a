import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Invalid email address');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number');

export const nameSchema = z
  .string()
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces');

export const slugSchema = z
  .string()
  .min(1, 'Slug is required')
  .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens');

// Content validation schemas
export const blogPostSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters').max(200, 'Title must be less than 200 characters'),
  slug: slugSchema,
  excerpt: z.string().max(500, 'Excerpt must be less than 500 characters').optional(),
  content: z.string().min(100, 'Content must be at least 100 characters'),
  category_id: z.string().uuid('Invalid category ID').optional(),
  featured_image: z.string().url('Invalid image URL').optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  is_featured: z.boolean().default(false),
  read_time: z.number().min(1, 'Read time must be at least 1 minute').default(5),
});

export const bookSummarySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  slug: slugSchema,
  author: z.string().min(1, 'Author is required').max(100, 'Author name must be less than 100 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  summary: z.string().min(100, 'Summary must be at least 100 characters'),
  cover_image: z.string().url('Invalid image URL').optional(),
  category_id: z.string().uuid('Invalid category ID').optional(),
  rating: z.number().min(0, 'Rating must be at least 0').max(5, 'Rating must be at most 5').default(0),
  pages: z.number().min(1, 'Pages must be at least 1').optional(),
  published_year: z.number().min(1000, 'Invalid year').max(new Date().getFullYear(), 'Year cannot be in the future').optional(),
  isbn: z.string().regex(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/, 'Invalid ISBN format').optional(),
  key_insights: z.array(z.string()).default([]),
  status: z.enum(['draft', 'published', 'archived']).default('published'),
});

export const businessIdeaSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters').max(200, 'Title must be less than 200 characters'),
  slug: slugSchema,
  description: z.string().min(50, 'Description must be at least 50 characters').max(1000, 'Description must be less than 1000 characters'),
  category_id: z.string().uuid('Invalid category ID').optional(),
  investment_level: z.enum(['Low', 'Medium', 'High']),
  difficulty: z.enum(['Beginner', 'Intermediate', 'Advanced']),
  potential_market: z.string().max(500, 'Market description must be less than 500 characters').optional(),
  required_skills: z.array(z.string()).default([]),
  estimated_revenue: z.string().max(100, 'Revenue estimate must be less than 100 characters').optional(),
  time_to_start: z.string().max(100, 'Time to start must be less than 100 characters').optional(),
  detailed_guide: z.string().optional(),
  resources: z.record(z.any()).default({}),
  status: z.enum(['draft', 'published', 'archived']).default('published'),
});

export const digitalProductSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters').max(200, 'Title must be less than 200 characters'),
  slug: slugSchema,
  description: z.string().min(50, 'Description must be at least 50 characters').max(1000, 'Description must be less than 1000 characters'),
  price: z.number().min(0, 'Price must be at least 0'),
  original_price: z.number().min(0, 'Original price must be at least 0').optional(),
  category_id: z.string().uuid('Invalid category ID').optional(),
  product_type: z.enum(['ebook', 'course', 'template', 'toolkit']),
  image: z.string().url('Invalid image URL').optional(),
  features: z.array(z.string()).default([]),
  file_url: z.string().url('Invalid file URL').optional(),
  file_size: z.string().optional(),
  rating: z.number().min(0, 'Rating must be at least 0').max(5, 'Rating must be at most 5').default(0),
  status: z.enum(['draft', 'published', 'archived']).default('published'),
});

export const categorySchema = z.object({
  name: z.string().min(2, 'Category name must be at least 2 characters').max(50, 'Category name must be less than 50 characters'),
  slug: slugSchema,
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').default('#3b82f6'),
});

export const profileSchema = z.object({
  full_name: nameSchema.optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  location: z.string().max(100, 'Location must be less than 100 characters').optional(),
  website: z.string().url('Invalid website URL').optional(),
  preferences: z.record(z.any()).default({}),
});

export const newsletterSchema = z.object({
  email: emailSchema,
  name: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name must be less than 50 characters').optional(),
});

export const contactSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters').max(2000, 'Message must be less than 2000 characters'),
});

// Utility functions for validation
export const validateEmail = (email: string): boolean => {
  return emailSchema.safeParse(email).success;
};

export const validatePassword = (password: string): boolean => {
  return passwordSchema.safeParse(password).success;
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

export const validateFileSize = (file: File, maxSizeMB: number): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};

export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type);
};

export const validateImageDimensions = (file: File, maxWidth: number, maxHeight: number): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve(img.width <= maxWidth && img.height <= maxHeight);
    };
    img.onerror = () => resolve(false);
    img.src = URL.createObjectURL(file);
  });
};