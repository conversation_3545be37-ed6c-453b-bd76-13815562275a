import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { analyticsApi } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';

// Google Analytics
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

export const Analytics = () => {
  const location = useLocation();
  const { user } = useAuth();

  useEffect(() => {
    // Initialize Google Analytics if tracking ID is provided
    const gaTrackingId = import.meta.env.VITE_GA_TRACKING_ID;
    if (gaTrackingId) {
      // Load Google Analytics script
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${gaTrackingId}`;
      document.head.appendChild(script);

      // Initialize gtag
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag() {
        window.dataLayer.push(arguments);
      };
      window.gtag('js', new Date());
      window.gtag('config', gaTrackingId, {
        page_title: document.title,
        page_location: window.location.href,
      });
    }

    // Initialize Hotjar if ID is provided
    const hotjarId = import.meta.env.VITE_HOTJAR_ID;
    if (hotjarId) {
      (function(h: any, o: any, t: any, j: any, a?: any, r?: any) {
        h.hj = h.hj || function() { (h.hj.q = h.hj.q || []).push(arguments) };
        h._hjSettings = { hjid: hotjarId, hjsv: 6 };
        a = o.getElementsByTagName('head')[0];
        r = o.createElement('script'); r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    }
  }, []);

  useEffect(() => {
    // Track page views
    const trackPageView = async () => {
      // Google Analytics
      if (window.gtag) {
        window.gtag('config', import.meta.env.VITE_GA_TRACKING_ID, {
          page_title: document.title,
          page_location: window.location.href,
          page_path: location.pathname,
        });
      }

      // Custom analytics
      await analyticsApi.trackEvent('page_view', {
        path: location.pathname,
        title: document.title,
        referrer: document.referrer,
        user_agent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      }, user?.id);
    };

    trackPageView();
  }, [location, user]);

  return null;
};

// Custom hook for tracking events
export const useAnalytics = () => {
  const { user } = useAuth();

  const trackEvent = async (eventName: string, properties: Record<string, any> = {}) => {
    // Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: properties.category || 'engagement',
        event_label: properties.label,
        value: properties.value,
        ...properties,
      });
    }

    // Custom analytics
    await analyticsApi.trackEvent(eventName, {
      ...properties,
      timestamp: new Date().toISOString(),
      url: window.location.href,
    }, user?.id);
  };

  const trackClick = (elementName: string, additionalProperties: Record<string, any> = {}) => {
    trackEvent('click', {
      element: elementName,
      category: 'interaction',
      ...additionalProperties,
    });
  };

  const trackSearch = (query: string, results: number, filters: Record<string, any> = {}) => {
    trackEvent('search', {
      search_term: query,
      results_count: results,
      filters,
      category: 'search',
    });
  };

  const trackContentView = (contentType: string, contentId: string, contentTitle: string) => {
    trackEvent('content_view', {
      content_type: contentType,
      content_id: contentId,
      content_title: contentTitle,
      category: 'content',
    });
  };

  const trackDownload = (fileName: string, fileType: string, fileSize?: string) => {
    trackEvent('download', {
      file_name: fileName,
      file_type: fileType,
      file_size: fileSize,
      category: 'download',
    });
  };

  const trackSignup = (method: string = 'email') => {
    trackEvent('sign_up', {
      method,
      category: 'authentication',
    });
  };

  const trackLogin = (method: string = 'email') => {
    trackEvent('login', {
      method,
      category: 'authentication',
    });
  };

  return {
    trackEvent,
    trackClick,
    trackSearch,
    trackContentView,
    trackDownload,
    trackSignup,
    trackLogin,
  };
};