{"name": "african-educational-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.0", "react-hook-form": "^7.47.0", "@supabase/supabase-js": "^2.39.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "react-helmet-async": "^2.0.4", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.16"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4"}}